'use client';

import { dialogClasses } from '@mui/material/Dialog';
import Stack from '@mui/material/Stack';
import dayjs from 'dayjs';
import { useRouter, useSearchParams } from 'next/navigation';
import { useState } from 'react';
import { QuerySortOperator } from '@nestjsx/crud-request';
import { Button } from '@/components/Atoms/Button/Button';
import ConfirmModal from '@/components/Atoms/ConfirmModal';
import Modal from '@/components/Atoms/Modal';
import Search from '@/components/Atoms/Search';
import Switch from '@/components/Atoms/Switch';
import Typography from '@/components/Atoms/Typography';
import { IssueCouponCodeModal } from '@/components/Molecules/Cashier/IssueCouponCodeModal/IssueCouponCodeModal';
import IssueCouponTable from '@/components/Molecules/IssueCouponTable';
import { IIssueCouponColumn } from '@/components/Molecules/IssueCouponTable/IssueCouponTable.types';
import { ModalSendCoupon } from '@/components/Molecules/ModalSendCoupons/ModalSendCoupon';
import { TableActions } from '@/components/Molecules/TableActions/TableActions';
import { ISSUE_COUPON_ID_KEY, PATH_NAME } from '@/lib/constants/pathname';
import { useAlert } from '@/lib/hooks/context/useAlert';
import { useMutationIssueCouponDelete, useMutationIssueCouponUpdate } from '@/lib/hooks/mutation/issue-coupon';
import { useIssueCoupon, useIssueCouponById, useIssueCouponCodes } from '@/lib/hooks/queries/issue-coupon';
import { TKeyOfOptions, useSetting } from '@/lib/hooks/queries/setting';
import { TCustomer } from '@/lib/types/entities/customer';
import { TCouponCode, TIssueCoupon } from '@/lib/types/entities/issue-coupon';
import { TStatus } from '@/lib/types/entities/utils';
import { IssueCouponForm } from '../IssueCouponForm/IssueCouponForm';
import {
  StyledButtonContainer,
  StyledContainer,
  StyledHeaderWrapper,
  StyledIssued,
  StyledSearchWrapper,
} from './IssueCoupon.styled';
import { useMe } from '@/lib/hooks/queries/me';
import { EPermission, EResourceAccess } from '@/lib/types/enum/role';
import { hasPermission } from '@/lib/utils/role';
import { TTransformPaginationResponse } from '@/lib/utils/transformResponse';
import { SWRKey } from '@/lib/constants/SWRKey';
import useDebounce from '@/lib/hooks/utils/useDebounce';

// TODO: integrate permission
type TActions = 'edit' | 'delete' | 'create' | 'edit';

type TTableActions = 'edit' | 'delete';
const OPTION_SELECT: TKeyOfOptions[] = ['status'];

export const IssueCouponListing: React.FC = () => {
  const [sortBy, setSortBy] = useState<undefined | string>(undefined);
  const [sortOrder, setSortOrder] = useState<QuerySortOperator>('ASC');
  const searchParams = useSearchParams();
  const tab = searchParams.get('tab') || '';
  const typeModal = searchParams.get('type') || '';
  const routeIssueCouponId = searchParams.get(ISSUE_COUPON_ID_KEY) || '';
  const [couponCodeKeySearch, setCouponCodeKeySearch] = useState<string>('');
  const debouncedCouponCodeSearch = useDebounce(couponCodeKeySearch, 300);

  const [couponKeySearch, setCouponKeySearch] = useState<string>('');
  const debouncedCouponSearch = useDebounce(couponKeySearch, 300);

  const { data: issueCouponData } = useIssueCoupon(debouncedCouponSearch, {
    query: sortBy ? { sort: [{ field: sortBy, order: sortOrder }] } : {},
  });
  const { data: issueCouponDetail } = useIssueCouponById(routeIssueCouponId);
  const { data: OPTION_BY_KEY } = useSetting(OPTION_SELECT);

  const { onDeleteIssueCoupon } = useMutationIssueCouponDelete();
  const { onUpdateIssueCoupon } = useMutationIssueCouponUpdate();
  const { showError, showSuccess } = useAlert();
  const router = useRouter();
  const { data: meData } = useMe();
  const resource = meData?.resource || [];

  const [confirmDelete, setConfirmDelete] = useState<{ isOpen: boolean; id: string }>({
    isOpen: false,
    id: '',
  });

  const [openSendManyCodes, setOpenSendManyCodes] = useState<{ isOpen: boolean; id: string }>({
    isOpen: false,
    id: '',
  });

  const onCloseModalSendMany = () => {
    setOpenSendManyCodes(pre => ({ ...pre, isOpen: false }));
  };

  const [couponCodesModal, setCouponCodesModal] = useState<{ id: string; isOpen: boolean }>({
    id: '',
    isOpen: false,
  });

  const [pagination, setPagination] = useState(1);
  const { data: couponCodeData, mutate: onRefetchCouponCodes } = useIssueCouponCodes<
    TTransformPaginationResponse<TCouponCode>
  >(couponCodesModal?.id, debouncedCouponCodeSearch, {
    pagination: {
      page: pagination,
      limit: SWRKey.COUPON.LIMIT,
    },
  });
  const couponCodes = couponCodeData?.data;
  const maxPage = couponCodeData?.pageCount || 2;

  const onDelete = async (id: string) => {
    setConfirmDelete({ isOpen: true, id });
  };

  const onUpdateStatus = async (payload: { id: string; status?: TStatus }) => {
    const response = await onUpdateIssueCoupon(payload);
    if (response.status === 'error') return showError({ title: 'Error' });
    showSuccess({ title: 'Updated !' });
  };

  // handle search with state + useSwr
  const onSearch = (keyword: string) => setCouponKeySearch(keyword);

  const onAddIssueCoupon = () => {
    router.push(PATH_NAME.ISSUE_COUPON.create(tab));
  };

  const onEdit = (id: string) => {
    router.push(PATH_NAME.ISSUE_COUPON.edit({ id, tab }));
  };

  const onConfirmDelete = async () => {
    const response = await onDeleteIssueCoupon(confirmDelete?.id);
    if (response.status === 'error') return showError({ title: 'Error' });
    setConfirmDelete(pre => ({ ...pre, isOpen: false, id: '' }));
    showSuccess({ title: 'Deleted !' });
  };

  const onCancelDelete = () => {
    setConfirmDelete(pre => ({ ...pre, isOpen: false, id: '' }));
  };

  const handleCloseModalDetail = () => {
    router.push(PATH_NAME.ISSUE_COUPON.all(tab));
  };

  const handleCodeSearch = (keyword: string) => {
    setCouponCodeKeySearch(keyword);
    setPagination(1);
  };

  const handleSendEmail = (id?: string) => {
    if (!id) return;
    setOpenSendManyCodes({ id, isOpen: true });
  };

  const handleOnSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(prevOrder => (prevOrder === 'ASC' ? 'DESC' : 'ASC'));
    } else {
      setSortBy(field);
      setSortOrder('DESC');
    }
  };

  const COLUMNS: IIssueCouponColumn[] = [
    {
      name: 'issueDate',
      label: 'ISSUE DATE',
      render: (row: TIssueCoupon) => (
        <Typography variant="body-xlarge-400">
          {dayjs(row?.issueDate).isValid() ? dayjs(row?.issueDate).format('DD/MM/YYYY') : ''}
        </Typography>
      ),
    },
    // {
    //   name: 'expiryDate',
    //   label: 'EXPIRY DATE',
    //   render: (row: TIssueCoupon) => (
    //     <Typography variant="body-xlarge-400">
    //       {dayjs(row?.expiryDate).isValid() ? dayjs(row?.expiryDate).format('DD/MM/YYYY') : ''}
    //     </Typography>
    //   ),
    // },
    {
      name: 'coupon',
      label: 'COUPON',
      render: (row: TIssueCoupon) => {
        // const period = row?.coupon?.period || 0;
        // const periodUnit = row?.coupon?.periodUnit || 'day';
        const isExpired = row?.isExpired;
        return (
          <Stack flexDirection="row" alignItems="center" gap={1.25}>
            <Typography variant="body-xlarge-400">{row?.coupon?.name || ''}</Typography>
            {isExpired && (
              <Typography
                variant="body-medium-400"
                color="white"
                sx={{ background: 'red', borderRadius: '10px', padding: '4px 12px' }}
              >
                Expired
              </Typography>
            )}
          </Stack>
        );
      },
    },
    {
      name: 'branches',
      label: 'BRANCH',
      render: (row: TIssueCoupon) => (
        <Typography variant="body-xlarge-400">{row?.branches?.map(branch => branch.name).join(', ')}</Typography>
      ),
    },
    {
      name: 'issued',
      label: 'ISSUED',
      render: (row: TIssueCoupon) => (
        <StyledIssued
          variant="heading-medium-700"
          onClick={() => {
            setCouponCodesModal(prev => ({ isOpen: true, id: row?.id }));
            // Refresh coupon codes data when modal is opened to show updated isUsed status
            onRefetchCouponCodes();
          }}
        >
          {row?.issued}
        </StyledIssued>
      ),
    },
    {
      name: 'used',
      label: 'USED',
      render: (row: TIssueCoupon) => <Typography variant="body-xlarge-400">{row?.used || 0}</Typography>,
    },
    {
      name: 'remain',
      label: 'REMAIN',
      render: (row: TIssueCoupon) => {
        const color = row?.remain > 0 ? 'green' : 'black';
        return (
          <Typography color={color} variant="body-xlarge-400">
            {row?.remain || 0}
          </Typography>
        );
      },
    },
    {
      name: 'status',
      label: 'STATUS',
      ordering: sortBy === 'status.name' && sortOrder === 'DESC' ? 'desc' : 'asc',
      onSortClick: handleOnSort,
      activeSort: true,
      sortColumnName: 'status.name',
      render: (row: TCustomer) => (
        <Switch
          checked={row.status?.name === 'Active'}
          onChange={(value: boolean) => {
            if (!hasPermission(resource, EResourceAccess['ISSUE_COUPON'], EPermission['ACTIVE'])) {
              return;
            }
            const newStatus = OPTION_BY_KEY?.status.find(s => s.id !== row?.status?.id);
            if (newStatus) onUpdateStatus({ id: row.id, status: newStatus });
          }}
        />
      ),
    },
    {
      name: 'actions',
      label: ' ',
      render: (row: TCustomer) => (
        <TableActions
          onEdit={
            hasPermission(resource, EResourceAccess['ISSUE_COUPON'], EPermission['UPDATE'])
              ? () => onEdit(row?.id)
              : undefined
          }
          onDelete={
            hasPermission(resource, EResourceAccess['ISSUE_COUPON'], EPermission['DELETE'])
              ? () => onDelete(row?.id)
              : undefined
          }
          onSendEmail={
            hasPermission(resource, EResourceAccess['ISSUE_COUPON'], EPermission['SEND_MAIL'])
              ? () => handleSendEmail(row?.id)
              : undefined
          }
        />
      ),
    },
  ];

  const isOpenModalCreate = typeModal === 'create';
  const isOpenModalEdit = typeModal === 'edit';

  return (
    <StyledContainer>
      <StyledHeaderWrapper>
        <StyledSearchWrapper>
          <Search placeholder="Search" onSearch={onSearch} />
        </StyledSearchWrapper>

        <StyledButtonContainer>
          {hasPermission(resource, EResourceAccess['ISSUE_COUPON'], EPermission['CREATE']) && (
            <Button color="primary" label="Add" onClick={onAddIssueCoupon} variant="contained" fullWidth={false} />
          )}
        </StyledButtonContainer>
      </StyledHeaderWrapper>
      <IssueCouponTable rows={issueCouponData || []} columns={COLUMNS} />
      <Modal
        isOpen={isOpenModalCreate || isOpenModalEdit}
        handleClose={handleCloseModalDetail}
        title={isOpenModalEdit ? 'EDIT ISSUE COUPON' : 'NEW ISSUE COUPON'}
        dialogProps={{
          sx: theme => ({
            color: theme.palette.primary.main,
            [`& .${dialogClasses.paper}`]: {
              padding: theme.spacing(3),
              margin: theme.spacing(3),
              background: theme.palette.neutrals.N100.main,
              height: 'fit-content',
              borderRadius: theme.spacing(1.25),
            },
          }),
        }}
      >
        <IssueCouponForm
          onCancel={handleCloseModalDetail}
          type={isOpenModalEdit ? 'edit' : 'create'}
          onSuccess={handleCloseModalDetail}
          defaultValues={issueCouponDetail}
        />
      </Modal>
      <ConfirmModal
        isOpen={confirmDelete.isOpen}
        title="Confirm delete issue coupon"
        bodyContent="Do you want to delete this issue coupon?"
        onCancel={() => onCancelDelete()}
        onClose={() => onCancelDelete()}
        onConfirm={() => onConfirmDelete()}
      />
      <IssueCouponCodeModal
        onSearch={handleCodeSearch}
        onRefetchCouponCodes={onRefetchCouponCodes}
        coupons={couponCodes}
        isOpen={couponCodesModal.isOpen}
        handleClose={() => {
          setCouponCodesModal({ id: '', isOpen: false });
          setPagination(1);
        }}
        title="Coupon codes"
        page={pagination}
        onLoadMore={() => {
          setPagination(pre => {
            const newPage = pre + 1;
            if (newPage > maxPage) return pre;
            return newPage;
          });
        }}
      />
      <ModalSendCoupon
        couponId={openSendManyCodes?.id}
        isOpen={openSendManyCodes?.isOpen}
        onClose={onCloseModalSendMany}
      />
    </StyledContainer>
  );
};
